// cloud/functions/travel/index.js
// 旅行数据相关云函数

const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// {{ AURA-X: Add - 初始化管理员权限数据库. Approval: 寸止(ID:1738056000). }}
// 初始化管理员权限的数据库实例
const adminDB = cloud.database({
  throwOnNotFound: false,
})

// 获取头像代理
async function getAvatarProxy(openid, data) {
  try {
    const { fileID } = data

    // 通过云函数获取文件临时链接
    const result = await cloud.getTempFileURL({
      fileList: [fileID]
    })

    if (result.fileList && result.fileList.length > 0) {
      const tempFileURL = result.fileList[0].tempFileURL
      return {
        success: true,
        data: {
          tempFileURL
        }
      }
    } else {
      throw new Error('获取临时链接失败')
    }
  } catch (error) {
    console.error('获取头像代理失败:', error)
    return {
      success: false,
      error: error.message || '获取头像失败'
    }
  }
}

// 上传头像到云存储
async function uploadAvatar(openid, data) {
  try {
    const { tempFilePath } = data

    if (!tempFilePath) {
      return {
        success: false,
        message: '缺少头像文件路径'
      }
    }

    // 上传到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: `avatars/${openid}_${Date.now()}.jpg`,
      filePath: tempFilePath
    })

    // 更新用户表中的头像信息
    await db.collection('users').where({ _openid: openid }).update({
      data: {
        avatarUrl: uploadResult.fileID,
        updateTime: new Date()
      }
    })

    return {
      success: true,
      data: {
        avatarUrl: uploadResult.fileID
      }
    }
  } catch (error) {
    console.error('上传头像失败:', error)
    return {
      success: false,
      message: error.message || '上传头像失败'
    }
  }
}

exports.main = async (event, context) => {
  const { action, data, options } = event
  const { OPENID } = cloud.getWXContext()

  try {
    switch (action) {
      case 'getTravelStatistics':
        return await getTravelStatistics(OPENID, data)
      case 'getTravelPlans':
        return await getTravelPlans(OPENID, data)
      case 'getCurrentPlan':
        return await getCurrentPlan(OPENID)
      case 'getOngoingPlans':
        return await getOngoingPlans(OPENID)
      case 'getTravelData':
        return await getTravelData(OPENID, data)
      case 'addTravelPlan':
        return await addTravelPlan(OPENID, data)
      case 'updateTravelPlan':
        return await updateTravelPlan(OPENID, data)
      case 'deleteTravelPlan':
        return await deleteTravelPlan(OPENID, data)

      // 协作功能相关接口
      case 'enableCollaboration':
        return await enableCollaboration(OPENID, data)
      case 'generateInviteCode':
        return await generateInviteCode(OPENID, data)
      case 'joinPlanByCode':
        return await joinPlanByCode(OPENID, data)
      case 'removeCollaborator':
        return await removeCollaborator(OPENID, data)
      case 'getCollaborators':
        return await getCollaborators(OPENID, data)
      case 'checkPlanUpdates':
        return await checkPlanUpdates(OPENID, data)
      case 'logOperation':
        return await logOperation(OPENID, data)
      case 'getOperationLog':
        return await getOperationLog(OPENID, data)
      case 'previewPlanByCode':
        return await previewPlanByCode(OPENID, data)
      case 'getTravelPlan':
        return await getTravelPlan(OPENID, data)
      case 'updateCollaboratorInfo':
        return await updateCollaboratorInfo(OPENID, data)
      case 'uploadAvatar':
        return await uploadAvatar(OPENID, data)

      case 'getAvatarProxy':
        return await getAvatarProxy(OPENID, data)

      default:
        return {
          success: false,
          message: '未知操作类型'
        }
    }
  } catch (error) {
    console.error('Travel云函数错误:', error)
    return {
      success: false,
      message: error.message || '服务器错误'
    }
  }
}

// 获取旅行统计数据
async function getTravelStatistics(openid, options = {}) {
  try {

    // {{ AURA-X: Modify - 支持协作计划的统计数据. Approval: 寸止(ID:1738056000). }}
    // 获取用户创建的旅行计划
    const createdPlansResult = await db.collection('travel_plans')
      .where({ _openid: openid })
      .get()

    // 获取用户作为协作者参与的旅行计划
    const collaboratedPlansResult = await db.collection('travel_plans')
      .where({
        'collaboration.enabled': true,
        'collaboration.collaborators.openid': openid
      })
      .get()

    // 合并计划并去重
    const allPlans = []
    const planIds = new Set()

    // 添加创建的计划
    createdPlansResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push(plan)
        planIds.add(plan._id)
      }
    })

    // 添加协作的计划
    collaboratedPlansResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push(plan)
        planIds.add(plan._id)
      }
    })

    const plans = allPlans

    // {{ AURA-X: Modify - 修复数据库集合和字段名. Approval: 寸止(ID:1738056000). }}
    // 获取所有相关计划的旅行支出记录
    let expenses = []
    if (planIds.size > 0) {
      const expenseResult = await db.collection('expense_records')
        .where({
          type: 'travel',
          travel_plan_id: _.in([...planIds])
        })
        .get()
      expenses = expenseResult.data || []
    }
    
    // 计算统计数据
    const totalPlans = plans.length
    const completedPlans = plans.filter(p => p.status === 'completed').length
    const ongoingPlans = plans.filter(p => p.status === 'ongoing').length
    const plannedPlans = plans.filter(p => p.status === 'planned').length
    
    const totalExpense = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0)
    const thisMonthExpense = expenses
      .filter(expense => {
        const expenseDate = new Date(expense.createTime)
        const now = new Date()
        return expenseDate.getMonth() === now.getMonth() && 
               expenseDate.getFullYear() === now.getFullYear()
      })
      .reduce((sum, expense) => sum + (expense.amount || 0), 0)
    
    // 计算平均每次旅行花费
    const avgExpensePerTrip = completedPlans > 0 ? totalExpense / completedPlans : 0

    // 获取用户预算设置
    const budgetResult = await db.collection('user_budgets')
      .where({ _openid: openid })
      .get()

    const userBudget = budgetResult.data.length > 0 ? budgetResult.data[0] : null
    const monthlyBudget = userBudget?.monthly || 0
    const travelBudgetRatio = userBudget?.travelRatio || 0.3 // 默认30%用于旅行
    const travelBudget = monthlyBudget * travelBudgetRatio

    // 计算预算使用率 - 修复计算逻辑
    let budgetUsage = 0
    if (travelBudget > 0 && thisMonthExpense > 0) {
      budgetUsage = (thisMonthExpense / travelBudget) * 100
      // 限制最大值，避免异常数据
      budgetUsage = Math.min(budgetUsage, 999)
    }
    const remainingBudget = Math.max(0, travelBudget - thisMonthExpense)

    // 获取最受欢迎的目的地
    const destinationCount = {}
    plans.forEach(plan => {
      if (plan.destination) {
        destinationCount[plan.destination] = (destinationCount[plan.destination] || 0) + 1
      }
    })
    
    const popularDestinations = Object.entries(destinationCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([destination, count]) => ({ destination, count }))
    
    // 数据验证和清理
    const cleanTotalExpense = Math.max(0, Math.round(totalExpense * 100) / 100)
    const cleanThisMonthExpense = Math.max(0, Math.round(thisMonthExpense * 100) / 100)
    const cleanTravelBudget = Math.max(0, Math.round(travelBudget * 100) / 100)
    const cleanBudgetUsage = Math.max(0, Math.min(999, Math.round(budgetUsage * 100) / 100))

    const statistics = {
      totalPlans: Math.max(0, totalPlans),
      completedPlans: Math.max(0, completedPlans),
      ongoingPlans: Math.max(0, ongoingPlans),
      plannedPlans: Math.max(0, plannedPlans),
      totalExpense: cleanTotalExpense,
      thisMonthExpense: cleanThisMonthExpense,
      avgExpensePerTrip: Math.max(0, Math.round(avgExpensePerTrip * 100) / 100),
      travelBudget: cleanTravelBudget,
      budgetUsage: cleanBudgetUsage,
      remainingBudget: Math.max(0, Math.round(remainingBudget * 100) / 100),
      popularDestinations: popularDestinations || []
    }

    return {
      success: true,
      data: statistics,
      version: Date.now() // 数据版本号
    }
    
  } catch (error) {
    console.error('获取旅行统计失败:', error)
    return {
      success: false,
      message: error.message || '获取旅行统计失败'
    }
  }
}

// {{ AURA-X: Modify - 支持获取协作者参与的计划. Approval: 寸止(ID:1738056000). }}
// 获取旅行计划列表
async function getTravelPlans(openid, options = {}) {
  try {
    const { limit = 20, skip = 0, status, orderBy = 'createTime', orderDirection = 'desc' } = options

    // 查询用户创建的计划
    let createdQuery = { _openid: openid }
    if (status) {
      createdQuery.status = status
    }

    const createdPlansResult = await db.collection('travel_plans')
      .where(createdQuery)
      .orderBy(orderBy, orderDirection)
      .get()

    // 查询用户作为协作者参与的计划
    let collaboratedQuery = {
      'collaboration.enabled': true,
      'collaboration.collaborators.openid': openid
    }
    if (status) {
      collaboratedQuery.status = status
    }

    const collaboratedPlansResult = await db.collection('travel_plans')
      .where(collaboratedQuery)
      .orderBy(orderBy, orderDirection)
      .get()

    // 合并结果并去重
    const allPlans = []
    const planIds = new Set()

    // 添加创建的计划
    createdPlansResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push({
          ...plan,
          userRole: 'creator' // 标记用户角色
        })
        planIds.add(plan._id)
      }
    })

    // 添加协作的计划
    collaboratedPlansResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push({
          ...plan,
          userRole: 'collaborator' // 标记用户角色
        })
        planIds.add(plan._id)
      }
    })

    // 按时间排序
    allPlans.sort((a, b) => {
      const aTime = new Date(a[orderBy])
      const bTime = new Date(b[orderBy])
      return orderDirection === 'desc' ? bTime - aTime : aTime - bTime
    })

    // 应用分页
    const paginatedPlans = allPlans.slice(skip, skip + limit)

    return {
      success: true,
      data: paginatedPlans,
      total: allPlans.length
    }

  } catch (error) {
    console.error('获取旅行计划失败:', error)
    return {
      success: false,
      message: error.message || '获取旅行计划失败'
    }
  }
}

// {{ AURA-X: Modify - 支持协作者的当前计划查询. Approval: 寸止(ID:1738056000). }}
// 获取当前计划
async function getCurrentPlan(openid) {
  try {
    // 查询用户创建的进行中计划
    const createdResult = await db.collection('travel_plans')
      .where({
        _openid: openid,
        status: 'ongoing'
      })
      .orderBy('startDate', 'asc')
      .get()

    // 查询用户作为协作者参与的进行中计划
    const collaboratedResult = await db.collection('travel_plans')
      .where({
        'collaboration.enabled': true,
        'collaboration.collaborators.openid': openid,
        status: 'ongoing'
      })
      .orderBy('startDate', 'asc')
      .get()

    // 合并结果并按开始时间排序
    const allPlans = [...createdResult.data, ...collaboratedResult.data]
    const uniquePlans = []
    const planIds = new Set()

    allPlans.forEach(plan => {
      if (!planIds.has(plan._id)) {
        uniquePlans.push(plan)
        planIds.add(plan._id)
      }
    })

    // 按开始时间排序，取最近的一个
    uniquePlans.sort((a, b) => new Date(a.startDate) - new Date(b.startDate))

    return {
      success: true,
      data: uniquePlans.length > 0 ? uniquePlans[0] : null
    }

  } catch (error) {
    console.error('获取当前计划失败:', error)
    return {
      success: false,
      message: error.message || '获取当前计划失败'
    }
  }
}

// {{ AURA-X: Modify - 支持协作者的进行中计划查询. Approval: 寸止(ID:1738056000). }}
// 获取进行中的计划
async function getOngoingPlans(openid) {
  try {
    // 查询用户创建的进行中计划
    const createdResult = await db.collection('travel_plans')
      .where({
        _openid: openid,
        status: 'ongoing'
      })
      .orderBy('startDate', 'asc')
      .get()

    // 查询用户作为协作者参与的进行中计划
    const collaboratedResult = await db.collection('travel_plans')
      .where({
        'collaboration.enabled': true,
        'collaboration.collaborators.openid': openid,
        status: 'ongoing'
      })
      .orderBy('startDate', 'asc')
      .get()

    // 合并结果并去重
    const allPlans = []
    const planIds = new Set()

    // 添加创建的计划
    createdResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push({
          ...plan,
          userRole: 'creator'
        })
        planIds.add(plan._id)
      }
    })

    // 添加协作的计划
    collaboratedResult.data.forEach(plan => {
      if (!planIds.has(plan._id)) {
        allPlans.push({
          ...plan,
          userRole: 'collaborator'
        })
        planIds.add(plan._id)
      }
    })

    // 按开始时间排序
    allPlans.sort((a, b) => new Date(a.startDate) - new Date(b.startDate))

    return {
      success: true,
      data: allPlans
    }

  } catch (error) {
    console.error('获取进行中计划失败:', error)
    return {
      success: false,
      message: error.message || '获取进行中计划失败'
    }
  }
}

// 获取综合旅行数据
async function getTravelData(openid, options = {}) {
  try {
    
    // 并行获取所有数据
    const [statisticsResult, plansResult, currentPlanResult, ongoingPlansResult] = await Promise.allSettled([
      getTravelStatistics(openid, options),
      getTravelPlans(openid, { limit: 5 }),
      getCurrentPlan(openid),
      getOngoingPlans(openid)
    ])
    
    const data = {
      statistics: statisticsResult.status === 'fulfilled' && statisticsResult.value.success ? 
                 statisticsResult.value.data : null,
      plans: plansResult.status === 'fulfilled' && plansResult.value.success ? 
             plansResult.value.data : [],
      currentPlan: currentPlanResult.status === 'fulfilled' && currentPlanResult.value.success ? 
                   currentPlanResult.value.data : null,
      ongoingPlans: ongoingPlansResult.status === 'fulfilled' && ongoingPlansResult.value.success ? 
                    ongoingPlansResult.value.data : []
    }
    
    return {
      success: true,
      data: data,
      version: Date.now()
    }
    
  } catch (error) {
    console.error('获取综合旅行数据失败:', error)
    return {
      success: false,
      message: error.message || '获取旅行数据失败'
    }
  }
}

// {{ AURA-X: Modify - 在创建计划时保存创建者信息. Approval: 寸止(ID:1738056000). }}
// 添加旅行计划
async function addTravelPlan(openid, planData) {
  try {
    // 获取创建者信息
    const creatorInfo = planData.creatorInfo || {
      nickname: '创建者',
      avatar: '/images/user.svg'
    }

    // {{ AURA-X: Modify - 智能状态判断逻辑. Approval: 寸止(ID:1738056000). }}
    const plan = {
      ...planData,
      _openid: openid,
      status: planData.status || determineInitialStatus(planData.startDate),
      createTime: new Date(),
      updateTime: new Date(),

      // 保存创建者信息
      creatorInfo: {
        nickname: creatorInfo.nickname || '创建者',
        avatar: creatorInfo.avatar || '/images/user.svg'
      },

      // 协作功能字段
      collaboration: {
        enabled: planData.enableCollaboration || false,  // 根据创建时的选择
        inviteCode: planData.enableCollaboration ? generateInviteCode() : null,
        creator: openid,          // 创建者openid
        collaborators: [],        // 协作者列表
        maxCollaborators: 5,      // 最大协作者数量
        inviteExpiry: planData.enableCollaboration ?
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : null // 30天后过期
      },

      // 操作日志
      operationLog: [
        {
          id: generateLogId(),
          operator: openid,
          operatorName: '创建者',
          action: '创建了旅行计划',
          details: `创建了旅行计划"${planData.title}"`,
          timestamp: new Date().toISOString(),
          changes: {
            field: 'plan',
            action: 'create',
            newValue: planData.title
          }
        }
      ]
    }

    const result = await db.collection('travel_plans').add({
      data: plan
    })

    return {
      success: true,
      data: {
        id: result._id,
        planId: result._id,
        inviteCode: plan.collaboration.inviteCode
      },
      message: '旅行计划创建成功'
    }

  } catch (error) {
    console.error('添加旅行计划失败:', error)
    return {
      success: false,
      message: error.message || '创建旅行计划失败'
    }
  }
}

// 生成操作日志ID
function generateLogId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
}

// 生成邀请码
function generateInviteCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 开启协作功能
async function enableCollaboration(openid, data) {
  try {
    const { planId } = data

    // 检查权限：只有创建者可以开启协作
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data || plan.data._openid !== openid) {
      return {
        success: false,
        message: '无权限操作'
      }
    }

    // 生成邀请码
    const inviteCode = generateInviteCode()
    const inviteExpiry = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后过期

    // 更新计划
    await db.collection('travel_plans').doc(planId).update({
      data: {
        'collaboration.enabled': true,
        'collaboration.inviteCode': inviteCode,
        'collaboration.inviteExpiry': inviteExpiry,
        updateTime: new Date()
      }
    })

    // 记录操作日志
    await logOperation(openid, {
      planId,
      action: '开启了协作功能',
      details: `生成邀请码: ${inviteCode}`,
      changes: {
        field: 'collaboration',
        action: 'enable',
        newValue: true
      }
    })

    return {
      success: true,
      data: {
        inviteCode,
        inviteExpiry
      },
      message: '协作功能已开启'
    }

  } catch (error) {
    console.error('开启协作功能失败:', error)
    return {
      success: false,
      message: error.message || '开启协作功能失败'
    }
  }
}

// 通过邀请码加入计划
async function joinPlanByCode(openid, data) {
  try {
    const { inviteCode, userInfo } = data

    // 查找对应的计划
    const planResult = await db.collection('travel_plans')
      .where({
        'collaboration.inviteCode': inviteCode,
        'collaboration.enabled': true
      })
      .get()

    if (planResult.data.length === 0) {
      return {
        success: false,
        message: '邀请码无效或已过期'
      }
    }

    const plan = planResult.data[0]

    // 检查邀请码是否过期
    if (plan.collaboration.inviteExpiry && new Date() > new Date(plan.collaboration.inviteExpiry)) {
      return {
        success: false,
        message: '邀请码已过期'
      }
    }

    // 检查是否已经是协作者
    const isAlreadyCollaborator = plan.collaboration.collaborators.some(c => c.openid === openid)
    if (isAlreadyCollaborator || plan._openid === openid) {
      return {
        success: false,
        message: '您已经是该计划的协作者'
      }
    }

    // 检查协作者数量限制
    if (plan.collaboration.collaborators.length >= plan.collaboration.maxCollaborators) {
      return {
        success: false,
        message: '协作者人数已达上限'
      }
    }

    // {{ AURA-X: Modify - 直接使用传递的用户信息，不依赖跨表查询. Approval: 寸止(ID:1738056000). }}
    // 添加协作者 - 直接使用前端传递的完整用户信息
    const newCollaborator = {
      openid: openid,
      nickname: userInfo.nickname || userInfo.nickName || '协作者',
      avatar: userInfo.avatar || userInfo.avatarUrl || '/images/user.svg',
      joinTime: new Date().toISOString(),
      permissions: ['edit', 'view', 'comment']
    }

    await db.collection('travel_plans').doc(plan._id).update({
      data: {
        'collaboration.collaborators': db.command.push(newCollaborator),
        updateTime: new Date()
      }
    })

    // 记录操作日志
    await logOperation(openid, {
      planId: plan._id,
      action: '加入了协作',
      details: `${userInfo.nickname || '新协作者'} 通过邀请码加入了计划`,
      changes: {
        field: 'collaborators',
        action: 'add',
        newValue: newCollaborator
      }
    })

    return {
      success: true,
      data: {
        planId: plan._id,
        planTitle: plan.title
      },
      message: '成功加入协作计划'
    }

  } catch (error) {
    console.error('加入计划失败:', error)
    return {
      success: false,
      message: error.message || '加入计划失败'
    }
  }
}

// 记录操作日志
async function logOperation(openid, data) {
  try {
    const { planId, action, details, changes } = data

    // 获取用户信息
    const userResult = await db.collection('users').where({ openid }).get()
    const userName = userResult.data.length > 0 ? userResult.data[0].nickname : '用户'

    const logEntry = {
      id: generateLogId(),
      operator: openid,
      operatorName: userName,
      action: action,
      details: details,
      timestamp: new Date().toISOString(),
      changes: changes || {}
    }

    // 添加到操作日志
    await db.collection('travel_plans').doc(planId).update({
      data: {
        operationLog: db.command.push(logEntry),
        updateTime: new Date()
      }
    })

    return {
      success: true,
      data: logEntry
    }

  } catch (error) {
    console.error('记录操作日志失败:', error)
    return {
      success: false,
      message: error.message || '记录操作日志失败'
    }
  }
}

// 检查计划更新
async function checkPlanUpdates(openid, data) {
  try {
    const { planId, lastSyncTime } = data

    // 获取计划信息
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    // 检查权限
    const hasPermission = plan.data._openid === openid ||
      plan.data.collaboration?.collaborators?.some(c => c.openid === openid)

    if (!hasPermission) {
      return {
        success: false,
        message: '无权限访问'
      }
    }

    // 检查是否有更新
    const planUpdateTime = new Date(plan.data.updateTime).getTime()
    const lastSync = new Date(lastSyncTime).getTime()
    const hasUpdates = planUpdateTime > lastSync

    let updates = {}
    if (hasUpdates) {
      // 获取最新的操作日志
      const recentLogs = plan.data.operationLog?.filter(log =>
        new Date(log.timestamp).getTime() > lastSync
      ) || []

      updates = {
        planData: plan.data,
        recentLogs: recentLogs,
        lastUpdateTime: plan.data.updateTime
      }
    }

    return {
      success: true,
      data: {
        hasUpdates,
        updates
      }
    }

  } catch (error) {
    console.error('检查计划更新失败:', error)
    return {
      success: false,
      message: error.message || '检查更新失败'
    }
  }
}

// 移除协作者
async function removeCollaborator(openid, data) {
  try {
    const { planId, collaboratorOpenid } = data

    // 获取计划信息
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    // 检查权限：只有创建者可以移除协作者
    if (plan.data._openid !== openid) {
      return {
        success: false,
        message: '无权限操作'
      }
    }

    // 移除协作者
    const updatedCollaborators = plan.data.collaboration.collaborators.filter(
      c => c.openid !== collaboratorOpenid
    )

    await db.collection('travel_plans').doc(planId).update({
      data: {
        'collaboration.collaborators': updatedCollaborators,
        updateTime: new Date()
      }
    })

    // 记录操作日志
    await logOperation(openid, {
      planId,
      action: '移除了协作者',
      details: `移除了协作者`,
      changes: {
        field: 'collaborators',
        action: 'remove',
        oldValue: collaboratorOpenid
      }
    })

    return {
      success: true,
      message: '协作者已移除'
    }

  } catch (error) {
    console.error('移除协作者失败:', error)
    return {
      success: false,
      message: error.message || '移除协作者失败'
    }
  }
}

// 获取协作者列表
async function getCollaborators(openid, data) {
  try {
    const { planId } = data

    // 获取计划信息
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    // 检查权限
    const hasPermission = plan.data._openid === openid ||
      plan.data.collaboration?.collaborators?.some(c => c.openid === openid)

    if (!hasPermission) {
      return {
        success: false,
        message: '无权限访问'
      }
    }

    return {
      success: true,
      data: {
        creator: {
          openid: plan.data._openid,
          isCreator: true
        },
        collaborators: plan.data.collaboration?.collaborators || [],
        maxCollaborators: plan.data.collaboration?.maxCollaborators || 5
      }
    }

  } catch (error) {
    console.error('获取协作者失败:', error)
    return {
      success: false,
      message: error.message || '获取协作者失败'
    }
  }
}

// 获取操作日志
async function getOperationLog(openid, data) {
  try {
    const { planId, limit = 20 } = data

    // 获取计划信息
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    // 检查权限
    const hasPermission = plan.data._openid === openid ||
      plan.data.collaboration?.collaborators?.some(c => c.openid === openid)

    if (!hasPermission) {
      return {
        success: false,
        message: '无权限访问'
      }
    }

    // 获取最近的操作日志
    const logs = plan.data.operationLog || []
    const recentLogs = logs
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)

    return {
      success: true,
      data: recentLogs
    }

  } catch (error) {
    console.error('获取操作日志失败:', error)
    return {
      success: false,
      message: error.message || '获取操作日志失败'
    }
  }
}

// 通过邀请码预览计划
async function previewPlanByCode(openid, data) {
  try {
    const { inviteCode } = data

    // 查找对应的计划
    const planResult = await db.collection('travel_plans')
      .where({
        'collaboration.inviteCode': inviteCode,
        'collaboration.enabled': true
      })
      .get()

    if (planResult.data.length === 0) {
      return {
        success: false,
        message: '邀请码无效或已过期'
      }
    }

    const plan = planResult.data[0]

    // 检查邀请码是否过期
    if (plan.collaboration.inviteExpiry && new Date() > new Date(plan.collaboration.inviteExpiry)) {
      return {
        success: false,
        message: '邀请码已过期'
      }
    }

    // {{ AURA-X: Modify - 直接使用计划中保存的创建者信息. Approval: 寸止(ID:1738056000). }}
    // 获取创建者信息 - 优先使用计划中保存的信息
    const creatorInfo = plan.creatorInfo || {
      nickname: '创建者',
      avatar: '/images/user.svg'
    }

    // 返回计划预览信息
    const previewData = {
      _id: plan._id,
      title: plan.title,
      destination: plan.destination,
      startDate: plan.startDate,
      endDate: plan.endDate,
      budget: plan.budget,
      budgetDetail: plan.budgetDetail,
      status: plan.status,
      collaboration: plan.collaboration,
      creatorInfo
    }

    return {
      success: true,
      data: previewData
    }

  } catch (error) {
    console.error('预览计划失败:', error)
    return {
      success: false,
      message: error.message || '预览计划失败'
    }
  }
}

// 获取单个旅行计划
async function getTravelPlan(openid, data) {
  try {
    const { planId } = data

    const result = await db.collection('travel_plans').doc(planId).get()

    if (!result.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    const plan = result.data

    // 检查权限
    const hasPermission = plan._openid === openid ||
      plan.collaboration?.collaborators?.some(c => c.openid === openid)

    if (!hasPermission) {
      return {
        success: false,
        message: '无权限访问'
      }
    }

    // 获取创建者信息
    let creatorInfo = {
      nickname: '创建者',
      avatar: '/images/user.svg'
    }

    // 从用户表获取创建者的最新信息
    try {
      const creatorResult = await db.collection('users').where({ _openid: plan._openid }).get()
      if (creatorResult.data.length > 0) {
        const creatorData = creatorResult.data[0]
        creatorInfo = {
          nickname: creatorData.nickname || creatorData.nickName || '创建者',
          avatar: creatorData.avatarUrl || creatorData.avatar || '/images/user.svg'
        }
      }
    } catch (error) {
      console.log('获取创建者信息失败，使用默认信息')
    }

    // 获取协作者的最新头像信息
    const collaboration = plan.collaboration || { enabled: false, collaborators: [] }
    if (collaboration.collaborators && collaboration.collaborators.length > 0) {
      // 并行获取所有协作者的最新信息
      collaboration.collaborators = await Promise.all(
        collaboration.collaborators.map(async (collaborator) => {
          try {
            const userResult = await db.collection('users').where({ _openid: collaborator.openid }).get()
            if (userResult.data.length > 0) {
              const userData = userResult.data[0]
              return {
                ...collaborator,
                nickname: userData.nickname || userData.nickName || collaborator.nickname,
                avatar: userData.avatarUrl || userData.avatar || '/images/user.svg'
              }
            }
          } catch (error) {
            console.log('获取协作者信息失败:', error)
          }
          return {
            ...collaborator,
            avatar: collaborator.avatar || '/images/user.svg'
          }
        })
      )
    }

    // 确保协作者数据被正确返回
    const returnData = {
      ...plan,
      creatorInfo,
      collaboration
    }

    return {
      success: true,
      data: returnData
    }

  } catch (error) {
    console.error('获取旅行计划失败:', error)
    return {
      success: false,
      message: error.message || '获取旅行计划失败'
    }
  }
}

// {{ AURA-X: Modify - 支持协作者编辑权限. Approval: 寸止(ID:1738056000). }}
// 更新旅行计划
async function updateTravelPlan(openid, data) {
  try {
    const { planId, ...updateData } = data

    // 首先获取计划信息，验证权限
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    // 检查权限：创建者或协作者都可以编辑
    const hasPermission = plan.data._openid === openid ||
      plan.data.collaboration?.collaborators?.some(c =>
        c.openid === openid && c.permissions?.includes('edit')
      )

    if (!hasPermission) {
      return {
        success: false,
        message: '无权限编辑此计划'
      }
    }

    updateData.updateTime = new Date()

    // 使用doc方式更新，避免权限限制
    const result = await db.collection('travel_plans').doc(planId).update({
      data: updateData
    })

    // 记录操作日志
    await logOperation(openid, {
      planId: planId,
      action: '更新了计划',
      details: '计划信息已更新',
      changes: {
        field: 'multiple',
        action: 'update',
        newValue: updateData
      }
    })

    return {
      success: true,
      message: '旅行计划更新成功'
    }

  } catch (error) {
    console.error('更新旅行计划失败:', error)
    return {
      success: false,
      message: error.message || '更新旅行计划失败'
    }
  }
}

// 删除旅行计划
async function deleteTravelPlan(openid, data) {
  try {
    const { planId } = data
    
    const result = await db.collection('travel_plans')
      .where({
        _id: planId,
        _openid: openid
      })
      .remove()
    
    if (result.stats.removed > 0) {
      return {
        success: true,
        message: '旅行计划删除成功'
      }
    } else {
      return {
        success: false,
        message: '计划不存在或无权限删除'
      }
    }
    
  } catch (error) {
    console.error('删除旅行计划失败:', error)
    return {
      success: false,
      message: error.message || '删除旅行计划失败'
    }
  }
}

// {{ AURA-X: Add - 更新协作者信息函数. Approval: 寸止(ID:1738056000). }}
// 更新协作者信息（主要用于修复头像）
async function updateCollaboratorInfo(openid, data) {
  try {
    const { planId } = data

    // 获取计划信息
    const plan = await db.collection('travel_plans').doc(planId).get()
    if (!plan.data) {
      return {
        success: false,
        message: '计划不存在'
      }
    }

    // 检查用户是否是协作者
    const collaborators = plan.data.collaboration?.collaborators || []
    const collaboratorIndex = collaborators.findIndex(c => c.openid === openid)

    if (collaboratorIndex === -1) {
      return {
        success: false,
        message: '您不是该计划的协作者'
      }
    }

    // 获取用户最新信息 - 使用管理员权限查询
    const userResult = await adminDB.collection('users').where({
      _openid: openid
    }).get()

    const userData = userResult.data.length > 0 ? userResult.data[0] : null

    if (userData) {
      // 更新协作者信息
      collaborators[collaboratorIndex] = {
        ...collaborators[collaboratorIndex],
        nickname: userData.nickname || userData.nickName || '协作者',
        avatar: userData.avatar || userData.avatarUrl || '/images/user.svg'
      }

      // 更新计划
      await db.collection('travel_plans').doc(planId).update({
        data: {
          'collaboration.collaborators': collaborators,
          updateTime: new Date()
        }
      })

      return {
        success: true,
        message: '协作者信息更新成功'
      }
    } else {
      return {
        success: false,
        message: '无法获取用户信息'
      }
    }

  } catch (error) {
    console.error('更新协作者信息失败:', error)
    return {
      success: false,
      message: error.message || '更新协作者信息失败'
    }
  }
}
