// components/icon/index.js
Component({
  properties: {
    // 图标名称
    name: {
      type: String,
      value: 'info'
    },
    // 图标大小 (rpx) - 标准尺寸38rpx
    size: {
      type: Number,
      value: 38
    },
    // 图标颜色
    color: {
      type: String,
      value: 'currentColor'
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 支持的图标列表 - 与实际SVG文件对应
    supportedIcons: [
      // 基础功能图标
      'user', 'settings', 'search', 'plus', 'bell', 'info', 'help-circle', 'shield', 'wechat',
      // 业务功能图标
      'wallet', 'chart', 'trending-up', 'location',
      // 旅行相关图标
      'airplane', 'suitcase', 'map', 'camera',
      // 社交相关图标
      'heart', 'friends',
      // 通讯相关图标
      'microphone', 'phone', 'message',
      // 数据统计图标
      'target', 'utensils', 'gamepad', 'more-horizontal', 'lightbulb', 'alert-triangle',
      // UI界面图标
      'home', 'arrow-right', 'star', 'close', 'check', 'edit', 'minus', 'delete', 'wallet', 'arrow-left', 'plus',
      // 其他图标
      'bookmark', 'calendar', 'car', 'clock', 'coffee', 'entertainment', 'food',
      'map-pin', 'music', 'photo', 'shopping-bag', 'switch', 'tag'
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击图标事件
     */
    onIconTap(e) {
      this.triggerEvent('tap', {
        name: this.properties.name,
        size: this.properties.size,
        color: this.properties.color
      });
    },

    /**
     * 检查图标是否支持
     */
    isIconSupported(iconName) {
      return this.data.supportedIcons.includes(iconName);
    },

    /**
     * 图片加载错误处理
     */
    onImageError(e) {
      // 静默处理图片加载失败
      // 可以在这里设置默认图标或者其他处理逻辑
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 检查图标名称是否支持
      if (!this.isIconSupported(this.properties.name)) {
        // 静默处理不支持的图标
      }
    }
  }
})
